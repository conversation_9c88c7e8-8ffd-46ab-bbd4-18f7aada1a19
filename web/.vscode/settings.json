{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "css.validate": false, "scss.validate": false, "less.validate": false, "stylelint.validate": ["css", "scss"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}}